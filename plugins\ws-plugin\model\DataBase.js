import { Config } from "../components/index.js"
import {
  saveMessage_id,
  findMessage_id,
  existSQL,
  findUser_id,
  saveUser_id,
  updateUser_id,
  findGroup_id,
  saveGroup_id,
  updateGroup_id,
} from "./db/index.js"

// 存储5分钟内的消息，按群组/用户ID分组，每个组内存储消息数组
let recentMessages = {}

// 定时清理过期的消息数据（每分钟清理一次）
setInterval(() => {
  const now = Math.floor(Date.now() / 1000)
  const fiveMinutesAgo = now - 5 * 60

  for (const id in recentMessages) {
    if (recentMessages[id]) {
      // 过滤掉5分钟前的消息
      recentMessages[id] = recentMessages[id].filter(msg => msg.time >= fiveMinutesAgo)
      // 如果数组为空，删除这个key
      if (recentMessages[id].length === 0) {
        delete recentMessages[id]
      }
    }
  }
}, 60 * 1000) // 每分钟执行一次清理

async function getMsg(where, other) {
  if (Object.hasOwnProperty.call(where, "message_id") && where.message_id == undefined) {
    return null
  }
  if (existSQL) {
    return await findMessage_id(where, other)
  } else {
    let key = where.onebot_id || where.message_id
    let msg = await redis.get(`Yz:ws-plugin:msg:${key}`)
    if (!msg) {
      return null
    }
    return JSON.parse(msg)
  }
}

async function setMsg(value) {
  if (Array.isArray(value.message_id) || !value.seq || !value.rand) {
    return
  }
  if (existSQL) {
    await saveMessage_id(value)
  } else {
    const EX = Config.msgStoreTime
    if (EX > 0) {
      await redis.set(`Yz:ws-plugin:msg:${value.onebot_id}`, JSON.stringify(value), { EX })
      await redis.set(`Yz:ws-plugin:msg:${value.message_id}`, JSON.stringify(value), { EX })
    }
  }
}

/**
 * 小于五分钟才会返回
 * @param {*} id
 * @returns
 */
function getLatestMsg(id) {
  const messages = recentMessages[id]
  if (!messages || messages.length === 0) {
    return null
  }

  const now = Math.floor(Date.now() / 1000)
  const fiveMinutesAgo = now - 5 * 60

  // 过滤5分钟内的消息，返回最新的一条
  const validMessages = messages.filter(msg => msg.time >= fiveMinutesAgo)

  if (validMessages.length === 0) {
    return null
  }

  // 返回最新的消息（保持原有接口不变）
  return validMessages[validMessages.length - 1]
}

/**
 * 设置对应的id的最新数据（现在存储5分钟内的所有消息）
 * @param {string|number} id 群组ID或用户ID
 * @param {Object} data 消息数据
 * @param {number} data.time 消息时间戳
 * @param {string|number} data.user_id 用户ID
 * @param {Function|null} data.reply 回复函数
 * @param {string} data.message_id 消息ID
 */
function setLatestMsg(id, data) {
  if (!recentMessages[id]) {
    recentMessages[id] = []
  }

  // 确保data有时间戳
  if (!data.time) {
    data.time = Math.floor(Date.now() / 1000)
  }

  // 添加消息到数组
  recentMessages[id].push(data)

  // 限制数组长度，避免内存过度使用（保留最近50条消息）
  if (recentMessages[id].length > 50) {
    recentMessages[id] = recentMessages[id].slice(-50)
  }
}

async function getUser_id(where) {
  if (where.user_id) {
    if (!isNaN(Number(where.user_id))) {
      return Number(where.user_id)
    }
    where.user_id = String(where.user_id)
  }
  let data = await findUser_id(where)
  if (!data) {
    if (where.user_id) {
      data = await saveUser_id(where.user_id)
    } else {
      return where.custom || where.id
    }
  }
  if (where.user_id) {
    return Number(data.custom) || data.id
  } else {
    return data.user_id
  }
}

async function setUser_id(where, custom) {
  const user_id = Number(custom)
  if (isNaN(user_id)) {
    return "输入有误,ID应为纯数字"
  }
  const result = await updateUser_id(where, user_id)
  if (result[0]) {
    return `修改成功~\n${where.user_id} => ${custom}`
  }
  return "修改失败,未包含此ID"
}

async function getGroup_id(where) {
  if (where.group_id) {
    if (!isNaN(Number(where.group_id))) {
      return Number(where.group_id)
    }
    where.group_id = String(where.group_id)
  }
  let data = await findGroup_id(where)
  if (!data) {
    if (where.group_id) {
      data = await saveGroup_id(where.group_id)
    } else {
      return where.custom || where.id
    }
  }
  if (where.group_id) {
    return Number(data.custom) || data.id
  } else {
    return data.group_id
  }
}

async function setGroup_id(where, custom) {
  const group_id = Number(custom)
  if (isNaN(group_id)) {
    return "输入有误,ID应为纯数字"
  }
  const result = await updateGroup_id(where, group_id)
  if (result[0]) {
    return `修改成功~\n${where.group_id} => ${custom}`
  }
  return "修改失败,未包含此ID"
}

export {
  getMsg,
  setMsg,
  getLatestMsg,
  setLatestMsg,
  getUser_id,
  setUser_id,
  getGroup_id,
  setGroup_id,
}
