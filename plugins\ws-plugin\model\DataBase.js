import { Config } from "../components/index.js"
import {
  saveMessage_id,
  findMessage_id,
  existSQL,
  findUser_id,
  saveUser_id,
  updateUser_id,
  findGroup_id,
  saveGroup_id,
  updateGroup_id,
} from "./db/index.js"

// 存储5分钟内的消息，按群组/用户ID分组，每个组内存储消息数组
let recentMessages = {}

// 定时清理过期的消息数据（每分钟清理一次）
setInterval(() => {
  const now = Math.floor(Date.now() / 1000)
  const fiveMinutesAgo = now - 5 * 60

  for (const id in recentMessages) {
    if (recentMessages[id]) {
      // 过滤掉5分钟前的消息
      recentMessages[id] = recentMessages[id].filter(msg => msg.time >= fiveMinutesAgo)
      // 如果数组为空，删除这个key
      if (recentMessages[id].length === 0) {
        delete recentMessages[id]
      }
    }
  }
}, 60 * 1000) // 每分钟执行一次清理

async function getMsg(where, other) {
  if (Object.hasOwnProperty.call(where, "message_id") && where.message_id == undefined) {
    return null
  }
  if (existSQL) {
    return await findMessage_id(where, other)
  } else {
    let key = where.onebot_id || where.message_id
    let msg = await redis.get(`Yz:ws-plugin:msg:${key}`)
    if (!msg) {
      return null
    }
    return JSON.parse(msg)
  }
}

async function setMsg(value) {
  if (Array.isArray(value.message_id) || !value.seq || !value.rand) {
    return
  }
  if (existSQL) {
    await saveMessage_id(value)
  } else {
    const EX = Config.msgStoreTime
    if (EX > 0) {
      await redis.set(`Yz:ws-plugin:msg:${value.onebot_id}`, JSON.stringify(value), { EX })
      await redis.set(`Yz:ws-plugin:msg:${value.message_id}`, JSON.stringify(value), { EX })
    }
  }
}

/**
 * 获取指定群组/频道中指定用户的最新消息（5分钟内）
 * @param {string|number} groupId 群组ID或频道ID
 * @param {string|number} userId 用户ID，如果不提供则返回该群组最新消息
 * @returns {Object|null} 消息数据或null
 */
function getLatestMsg(groupId, userId = null) {
  const messages = recentMessages[groupId]
  if (!messages || messages.length === 0) {
    return null
  }

  const now = Math.floor(Date.now() / 1000)
  const fiveMinutesAgo = now - 5 * 60

  // 过滤5分钟内的消息
  const validMessages = messages.filter(msg => msg.time >= fiveMinutesAgo)

  if (validMessages.length === 0) {
    return null
  }

  // 如果指定了用户ID，查找该用户的最新消息
  if (userId) {
    const userMessages = validMessages.filter(msg => msg.user_id === userId)
    return userMessages.length > 0 ? userMessages[userMessages.length - 1] : null
  }

  // 如果没有指定用户ID，返回最新的消息
  return validMessages[validMessages.length - 1]
}

/**
 * 添加消息到指定群组/频道的消息列表中
 * @param {string|number} groupId 群组ID或频道ID
 * @param {Object} data 消息数据
 * @param {number} data.time 消息时间戳
 * @param {string|number} data.user_id 用户ID
 * @param {Function|null} data.reply 回复函数
 * @param {string} data.message_id 消息ID
 */
function setLatestMsg(groupId, data) {
  if (!recentMessages[groupId]) {
    recentMessages[groupId] = []
  }

  // 添加消息到数组
  recentMessages[groupId].push(data)

  // 限制数组长度，避免内存过度使用（保留最近100条消息）
  if (recentMessages[groupId].length > 100) {
    recentMessages[groupId] = recentMessages[groupId].slice(-100)
  }
}

/**
 * 根据消息ID获取消息上下文（用于回复时正确艾特）
 * @param {string} messageId 消息ID
 * @returns {Object|null} 消息上下文或null
 */
function getMessageContext(messageId) {
  for (const groupId in recentMessages) {
    const messages = recentMessages[groupId]
    if (messages) {
      const message = messages.find(msg => msg.message_id === messageId)
      if (message) {
        return message
      }
    }
  }
  return null
}

async function getUser_id(where) {
  if (where.user_id) {
    if (!isNaN(Number(where.user_id))) {
      return Number(where.user_id)
    }
    where.user_id = String(where.user_id)
  }
  let data = await findUser_id(where)
  if (!data) {
    if (where.user_id) {
      data = await saveUser_id(where.user_id)
    } else {
      return where.custom || where.id
    }
  }
  if (where.user_id) {
    return Number(data.custom) || data.id
  } else {
    return data.user_id
  }
}

async function setUser_id(where, custom) {
  const user_id = Number(custom)
  if (isNaN(user_id)) {
    return "输入有误,ID应为纯数字"
  }
  const result = await updateUser_id(where, user_id)
  if (result[0]) {
    return `修改成功~\n${where.user_id} => ${custom}`
  }
  return "修改失败,未包含此ID"
}

async function getGroup_id(where) {
  if (where.group_id) {
    if (!isNaN(Number(where.group_id))) {
      return Number(where.group_id)
    }
    where.group_id = String(where.group_id)
  }
  let data = await findGroup_id(where)
  if (!data) {
    if (where.group_id) {
      data = await saveGroup_id(where.group_id)
    } else {
      return where.custom || where.id
    }
  }
  if (where.group_id) {
    return Number(data.custom) || data.id
  } else {
    return data.group_id
  }
}

async function setGroup_id(where, custom) {
  const group_id = Number(custom)
  if (isNaN(group_id)) {
    return "输入有误,ID应为纯数字"
  }
  const result = await updateGroup_id(where, group_id)
  if (result[0]) {
    return `修改成功~\n${where.group_id} => ${custom}`
  }
  return "修改失败,未包含此ID"
}

export {
  getMsg,
  setMsg,
  getLatestMsg,
  setLatestMsg,
  getMessageContext,
  getUser_id,
  setUser_id,
  getGroup_id,
  setGroup_id,
}
