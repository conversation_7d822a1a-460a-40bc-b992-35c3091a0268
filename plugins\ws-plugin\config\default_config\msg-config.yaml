#以数组内开头的消息不上传
noMsgStart:
  - "示例1"

#包含了数组内的消息不上传
noMsgInclude:
  - 示例1

# 通知哪个主人 
howToMaster: 1

#断开连接时否通知主人
disconnectToMaster: false

#重新连接成功时是否通知主人
reconnectToMaster: false

#首次连接时是否通知主人成功还是失败
firstconnectToMaster: false

#消息存储时间,用于撤回和回复消息,单位秒
msgStoreTime: 300

#黑名单群
noGroup:
  - 123456

#白名单群
yesGroup: []

#被禁言或者全体禁言时候拦截消息不再上报
muteStop: false

# 拦截临时会话消息上报
tempMsgReport: false

#red 发送伪造转发消息方式 1:伪造转发 2:直接发送
redSendForwardMsgType: 1

# 图片渲染精度
renderScale: 100

# 数据库同步锁最多可同时执行多少次
taskQueue: 1

# 文字转图片是否展示ID 0:不展示 1:仅ID 2:提示+ID
toImgID: 0

# 转图片是否不包含标题 0:包含 1:不包含(标题+图混合)
toImgNoTitle: 0